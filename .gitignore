# Dependencies
node_modules/
backend/node_modules/

# Environment variables
.env
backend/.env

# Build outputs
dist/
build/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vite
.vite/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Uploaded files
backend/uploads/*
!backend/uploads/.gitkeep

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp

# Package lock files (optional - you can keep these)
# package-lock.json
# backend/package-lock.json
