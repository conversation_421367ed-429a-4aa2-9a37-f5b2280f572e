@echo off
echo 🚀 Starting Freelancer-market-place System (Local Mode - No MongoDB Atlas Required)...

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js is available

echo 🧹 Cleaning up existing processes...
taskkill /f /im node.exe >nul 2>&1

echo.
echo 🖥️ Starting Backend Server (Local Storage)...
echo Backend will run on: http://localhost:3002
echo Database: Local Storage (No MongoDB required)
echo ⏳ Waiting for backend to initialize...

REM Start backend server
start "Freelancer-market-place Backend (Local)" cmd /k "cd /d "%~dp0backend" && node server-local.js"

REM Wait for backend to start
timeout /t 5 /nobreak >nul

echo.
echo 🌐 Starting Frontend Server...
echo Frontend will run on: http://localhost:5173
echo ⏳ Waiting for frontend to initialize...

REM Start frontend server
start "Freelancer-market-place Frontend" cmd /k "cd /d "%~dp0" && npx vite"

REM Wait for frontend to start
timeout /t 3 /nobreak >nul

echo.
echo 🎉 Freelancer-market-place is starting up!

echo.
echo 🌐 System URLs:
echo   Frontend: http://localhost:5173/
echo   Backend:  http://localhost:3002/api/health
echo   Database: Local Storage (No external database required)

echo.
echo 🔑 Your Login Credentials:
echo   Recruiter:   <EMAIL> / 987654
echo   Job Seeker:  <EMAIL> / 123456

echo.
echo 📊 Database Features:
echo   ✅ User registration works locally
echo   ✅ Job posting works locally
echo   ✅ Job applications work locally
echo   ✅ All data stored in memory (resets on restart)

echo.
echo 🌐 Opening browser in 3 seconds...
timeout /t 3 /nobreak >nul
start http://localhost:5173/

echo.
echo ✅ Freelancer-market-place Started Successfully!

echo.
echo 💡 Tips:
echo   - Keep both terminal windows open
echo   - All data is stored locally (no cloud database needed)
echo   - Data resets when you restart the application
echo   - To stop: Close both terminal windows or press Ctrl+C in each
echo   - This version works completely offline

echo.
echo 🔧 To check system status:
echo   Backend Health: http://localhost:3002/api/health
echo   Frontend: http://localhost:5173/

pause
