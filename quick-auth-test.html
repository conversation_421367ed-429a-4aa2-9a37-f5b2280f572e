<!DOCTYPE html>
<html>
<head>
    <title>Quick Auth Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔐 Quick Authentication Test</h1>
    <p>Testing: Frontend (localhost:5173) → Backend (localhost:3002)</p>
    
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testRegister()">Test Register</button>
    
    <div id="result"></div>

    <script>
        async function testLogin() {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="result">🔄 Testing login...</div>';
            
            try {
                const response = await fetch('http://localhost:3002/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '987654'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="result success">✅ Login Success!<br>User: ${data.user.full_name}<br>Token: ${data.token ? 'Received' : 'Missing'}</div>`;
                } else {
                    result.innerHTML = `<div class="result error">❌ Login Failed: ${data.error}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ Network Error: ${error.message}<br><br><strong>This is the same error you see in the main app!</strong><br>Possible causes:<br>1. Backend not running<br>2. CORS issues<br>3. Port conflicts</div>`;
            }
        }

        async function testRegister() {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="result">🔄 Testing registration...</div>';
            
            try {
                const response = await fetch('http://localhost:3002/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: `test${Date.now()}@example.com`,
                        password: 'password123',
                        fullName: 'Test User',
                        userType: 'jobseeker'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="result success">✅ Registration Success!<br>User ID: ${data.user._id}<br>Token: ${data.token ? 'Received' : 'Missing'}</div>`;
                } else {
                    result.innerHTML = `<div class="result error">❌ Registration Failed: ${data.error}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="result error">❌ Network Error: ${error.message}</div>`;
            }
        }

        // Auto-test on load
        window.onload = () => testLogin();
    </script>
</body>
</html>
