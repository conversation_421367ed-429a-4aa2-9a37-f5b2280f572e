const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Freelancer-market-place Server (Local MongoDB)...');

const app = express();
const PORT = process.env.PORT || 3002;
const JWT_SECRET = 'your-local-jwt-secret-key';

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}

// Local MongoDB connection (no Atlas required)
const MONGODB_LOCAL_URI = 'mongodb://localhost:27017/freelancer-market';

// In-memory storage for development (no MongoDB required)
let users = [];

// Initialize users with proper password hashes
async function initializeUsers() {
  const recruiterPassword = await hashPassword('987654');
  const jobseekerPassword = await hashPassword('123456');

  users = [
    {
      _id: '1',
      email: '<EMAIL>',
      password: recruiterPassword,
      full_name: 'Manish Modi',
      user_type: 'recruiter',
      company_name: 'The Tech World',
      created_at: new Date()
    },
    {
      _id: '2',
      email: '<EMAIL>',
      password: jobseekerPassword,
      full_name: 'Manish Kumar',
      user_type: 'jobseeker',
      created_at: new Date()
    }
  ];
}

let jobs = [
  {
    _id: '1',
    title: 'Frontend Developer',
    company: 'Tech Solutions',
    location: 'Remote',
    experience: '2+ years',
    skills: 'React, JavaScript, CSS',
    description: 'We are looking for a skilled Frontend Developer...',
    salary_range: '$60,000 - $80,000',
    job_type: 'full-time',
    recruiter_id: '1',
    status: 'active',
    created_at: new Date()
  },
  {
    _id: '2',
    title: 'Backend Developer',
    company: 'Digital Agency',
    location: 'New York',
    experience: '3+ years',
    skills: 'Node.js, Express, MongoDB',
    description: 'Join our team as a Backend Developer...',
    salary_range: '$70,000 - $90,000',
    job_type: 'full-time',
    recruiter_id: '1',
    status: 'active',
    created_at: new Date()
  }
];

let applications = [];
let nextId = 3;

// Helper function to generate ID
const generateId = () => (nextId++).toString();

// Helper function to hash password
const hashPassword = async (password) => {
  return await bcrypt.hash(password, 10);
};

// Helper function to compare password
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

// Middleware to authenticate token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'resume-' + uniqueSuffix + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf' || file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF and image files are allowed'));
    }
  }
});

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Freelancer-market-place API is running (Local Mode)',
    timestamp: new Date().toISOString(),
    database: 'Local Storage'
  });
});

// Auth routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, fullName, userType, companyName } = req.body;

    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create new user
    const newUser = {
      _id: generateId(),
      email,
      password: hashedPassword,
      full_name: fullName,
      user_type: userType,
      company_name: companyName || null,
      created_at: new Date()
    };

    users.push(newUser);

    // Generate token
    const token = jwt.sign(
      { userId: newUser._id, email: newUser.email, userType: newUser.user_type },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: newUser._id,
        email: newUser.email,
        fullName: newUser.full_name,
        userType: newUser.user_type,
        companyName: newUser.company_name
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await comparePassword(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate token
    const token = jwt.sign(
      { userId: user._id, email: user.email, userType: user.user_type },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        email: user.email,
        fullName: user.full_name,
        userType: user.user_type,
        companyName: user.company_name
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Jobs routes
app.get('/api/jobs', (req, res) => {
  try {
    const formattedJobs = jobs.map(job => ({
      ...job,
      skills: job.skills ? job.skills.split(',') : []
    }));
    res.json(formattedJobs);
  } catch (error) {
    console.error('Get jobs error:', error);
    res.status(500).json({ error: 'Failed to fetch jobs' });
  }
});

app.post('/api/jobs', authenticateToken, (req, res) => {
  try {
    if (req.user.userType !== 'recruiter') {
      return res.status(403).json({ error: 'Only recruiters can post jobs' });
    }

    const { title, company, location, experience, skills, description, salaryRange, jobType } = req.body;
    const skillsString = Array.isArray(skills) ? skills.join(',') : skills;

    const newJob = {
      _id: generateId(),
      title,
      company,
      location,
      experience,
      skills: skillsString,
      description,
      salary_range: salaryRange || null,
      job_type: jobType || 'full-time',
      recruiter_id: req.user.userId,
      status: 'active',
      created_at: new Date()
    };

    jobs.push(newJob);

    res.status(201).json({
      message: 'Job created successfully',
      jobId: newJob._id
    });
  } catch (error) {
    console.error('Create job error:', error);
    res.status(500).json({ error: 'Failed to create job' });
  }
});

app.get('/api/jobs/recruiter/my-jobs', authenticateToken, (req, res) => {
  try {
    if (req.user.userType !== 'recruiter') {
      return res.status(403).json({ error: 'Only recruiters can access this endpoint' });
    }

    const recruiterJobs = jobs.filter(job => job.recruiter_id === req.user.userId);
    const formattedJobs = recruiterJobs.map(job => ({
      ...job,
      application_count: applications.filter(app => app.job_id === job._id).length,
      skills: job.skills ? job.skills.split(',') : []
    }));

    res.json(formattedJobs);
  } catch (error) {
    console.error('Get recruiter jobs error:', error);
    res.status(500).json({ error: 'Failed to fetch jobs' });
  }
});

// Applications routes
app.post('/api/applications', authenticateToken, upload.single('resume'), (req, res) => {
  try {
    if (req.user.userType !== 'jobseeker') {
      return res.status(403).json({ error: 'Only job seekers can apply for jobs' });
    }

    const { jobId } = req.body;
    const resumeFile = req.file;

    if (!resumeFile) {
      return res.status(400).json({ error: 'Resume file is required' });
    }

    // Check if job exists
    const job = jobs.find(j => j._id === jobId);
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Check if already applied
    const existingApplication = applications.find(
      app => app.job_id === jobId && app.jobseeker_id === req.user.userId
    );
    if (existingApplication) {
      return res.status(400).json({ error: 'You have already applied for this job' });
    }

    const newApplication = {
      _id: generateId(),
      job_id: jobId,
      jobseeker_id: req.user.userId,
      resume_filename: resumeFile.filename,
      status: 'applied',
      applied_date: new Date()
    };

    applications.push(newApplication);

    res.status(201).json({
      message: 'Application submitted successfully',
      applicationId: newApplication._id
    });
  } catch (error) {
    console.error('Apply for job error:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
});

app.get('/api/applications/my-applications', authenticateToken, (req, res) => {
  try {
    if (req.user.userType !== 'jobseeker') {
      return res.status(403).json({ error: 'Only job seekers can access this endpoint' });
    }

    const userApplications = applications.filter(app => app.jobseeker_id === req.user.userId);
    const formattedApplications = userApplications.map(app => {
      const job = jobs.find(j => j._id === app.job_id);
      return {
        ...app,
        job_title: job ? job.title : 'Unknown Job',
        company: job ? job.company : 'Unknown Company'
      };
    });

    res.json(formattedApplications);
  } catch (error) {
    console.error('Get applications error:', error);
    res.status(500).json({ error: 'Failed to fetch applications' });
  }
});

// Start server
async function startServer() {
  await initializeUsers();

  app.listen(PORT, () => {
    console.log('✅ Local storage initialized');
    console.log(`📊 Test data: ${users.length} users, ${jobs.length} jobs`);
    console.log('🎉 ===== FREELANCER-MARKET-PLACE SERVER READY =====');
    console.log(`🚀 Server: http://localhost:${PORT}`);
    console.log(`📊 Health: http://localhost:${PORT}/api/health`);
    console.log(`🌐 Frontend: http://localhost:5173`);
    console.log(`🗄️ Database: Local Storage (No MongoDB required)`);
    console.log('');
    console.log('🔑 Test Credentials:');
    console.log('   Recruiter: <EMAIL> / 987654');
    console.log('   Job Seeker: <EMAIL> / 123456');
    console.log('');
    console.log('⚡ Ready to receive requests!');
    console.log('=====================================');
  });
}

startServer();

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (error) => {
  console.error('Unhandled Rejection:', error);
});
