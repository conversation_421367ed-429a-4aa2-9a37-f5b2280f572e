const axios = require('axios');

const API_BASE = 'http://localhost:3002/api';

async function testLocalBackend() {
  try {
    console.log('🔍 Testing Local Backend...\n');

    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health check passed:', healthResponse.data);

    // Test 2: Get jobs
    console.log('\n2️⃣ Testing jobs endpoint...');
    const jobsResponse = await axios.get(`${API_BASE}/jobs`);
    console.log('✅ Jobs loaded:', jobsResponse.data.length, 'jobs found');

    // Test 3: Login
    console.log('\n3️⃣ Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: '987654'
    });
    console.log('✅ Login successful for:', loginResponse.data.user.fullName);
    const token = loginResponse.data.token;

    // Test 4: Post a job
    console.log('\n4️⃣ Testing job posting...');
    const jobData = {
      title: 'Test Local Job',
      company: 'Local Test Company',
      location: 'Remote',
      experience: '1+ years',
      skills: ['JavaScript', 'React'],
      description: 'This is a test job posted via local backend.',
      salaryRange: '$50,000 - $70,000',
      jobType: 'full-time'
    };

    const jobResponse = await axios.post(`${API_BASE}/jobs`, jobData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Job posted successfully! Job ID:', jobResponse.data.jobId);

    // Test 5: Register new user
    console.log('\n5️⃣ Testing user registration...');
    const registerData = {
      email: '<EMAIL>',
      password: 'testpass123',
      fullName: 'Test User',
      userType: 'jobseeker'
    };

    const registerResponse = await axios.post(`${API_BASE}/auth/register`, registerData);
    console.log('✅ User registered successfully:', registerResponse.data.user.fullName);

    console.log('\n🎉 All tests passed! Local backend is working perfectly.');
    console.log('\n📋 Summary:');
    console.log('   ✅ Health check: Working');
    console.log('   ✅ Jobs loading: Working');
    console.log('   ✅ User login: Working');
    console.log('   ✅ Job posting: Working');
    console.log('   ✅ User registration: Working');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testLocalBackend();
