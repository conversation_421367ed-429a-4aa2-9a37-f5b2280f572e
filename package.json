{"name": "freelancer-market-place", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5173 --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "npm run dev", "start:backend": "cd backend && node server.js", "start:full": "concurrently \"npm run start:backend\" \"npm run dev\"", "stop": "taskkill /f /im node.exe", "check": "node check-system.js"}, "dependencies": {"axios": "^1.9.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^5.2.0"}}