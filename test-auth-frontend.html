<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    
    <div class="section">
        <h2>Backend Health Check</h2>
        <button onclick="testHealth()">Test Health</button>
        <div id="healthResult" class="result"></div>
    </div>

    <div class="section">
        <h2>Test Login</h2>
        <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="loginPassword" placeholder="Password" value="987654">
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>Test Registration</h2>
        <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="regPassword" placeholder="Password" value="123456">
        <input type="text" id="regFullName" placeholder="Full Name" value="Test User">
        <select id="regUserType">
            <option value="jobseeker">Job Seeker</option>
            <option value="recruiter">Recruiter</option>
        </select>
        <input type="text" id="regCompany" placeholder="Company (for recruiters)" value="Test Company">
        <button onclick="testRegister()">Test Register</button>
        <div id="registerResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3002/api';

        async function testHealth() {
            const result = document.getElementById('healthResult');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                result.className = 'result success';
                result.innerHTML = `✅ Health check successful:<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Health check failed: ${error.message}`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('loginResult');
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.className = 'result success';
                    result.innerHTML = `✅ Login successful:<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ Login failed:<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Login error: ${error.message}`;
            }
        }

        async function testRegister() {
            const result = document.getElementById('registerResult');
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const fullName = document.getElementById('regFullName').value;
            const userType = document.getElementById('regUserType').value;
            const companyName = document.getElementById('regCompany').value;
            
            const userData = {
                email,
                password,
                fullName,
                userType
            };
            
            if (userType === 'recruiter' && companyName) {
                userData.companyName = companyName;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.className = 'result success';
                    result.innerHTML = `✅ Registration successful:<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ Registration failed:<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Registration error: ${error.message}`;
            }
        }

        // Auto-test health on page load
        window.onload = () => {
            testHealth();
        };
    </script>
</body>
</html>
